<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
    <% // 解决中文乱码问题 request.setCharacterEncoding("UTF-8"); response.setCharacterEncoding("UTF-8"); //
        检查用户是否已登录（从Session中获取用户名） String sessionUsername=(String) session.getAttribute("username"); java.util.Date
        loginTime=(java.util.Date) session.getAttribute("loginTime"); // 如果没有登录，重定向到登录页面 if (sessionUsername==null) {
        response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode(" 请先登录！", "UTF-8" )); return; } %>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title>系统主页</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f8f9fa;
                    margin: 0;
                    padding: 20px;
                }

                .header {
                    background-color: #007bff;
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 20px;
                    text-align: center;
                }

                .welcome-container {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    margin-bottom: 20px;
                }

                .user-info {
                    background-color: #e9ecef;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }

                .nav-menu {
                    background-color: white;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }

                .nav-menu ul {
                    list-style-type: none;
                    padding: 0;
                }

                .nav-menu li {
                    margin: 10px 0;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                }

                .nav-menu a {
                    text-decoration: none;
                    color: #007bff;
                    font-weight: bold;
                }

                .nav-menu a:hover {
                    color: #0056b3;
                }

                .logout-btn {
                    background-color: #dc3545;
                    color: white;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-block;
                    margin-top: 20px;
                }

                .logout-btn:hover {
                    background-color: #c82333;
                }

                .session-info {
                    font-size: 14px;
                    color: #666;
                    margin-top: 10px;
                }
            </style>
        </head>

        <body>
            <div class="header">
                <h1>欢迎来到系统主页</h1>
            </div>

            <div class="welcome-container">
                <h2>登录成功！</h2>
                <div class="user-info">
                    <h3>用户信息：</h3>
                    <p><strong>用户名：</strong>
                        <%= sessionUsername %>
                    </p>
                    <% if (loginTime !=null) { %>
                        <p><strong>登录时间：</strong>
                            <%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(loginTime) %>
                        </p>
                        <% } %>
                            <div class="session-info">
                                <p><strong>Session ID：</strong>
                                    <%= session.getId() %>
                                </p>
                                <p><strong>Session创建时间：</strong>
                                    <%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new
                                        java.util.Date(session.getCreationTime())) %>
                                </p>
                                <p><strong>最后访问时间：</strong>
                                    <%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new
                                        java.util.Date(session.getLastAccessedTime())) %>
                                </p>
                            </div>
                </div>

                <p>您已成功登录系统！现在可以访问系统的各项功能。</p>
            </div>

            <div class="nav-menu">
                <h3>系统功能菜单：</h3>
                <ul>
                    <li><a href="register.html">用户注册</a> - 注册新用户</li>
                </ul>

                <a href="logout.jsp" class="logout-btn">安全退出</a>
            </div>

            <script>
                // 显示当前时间
                function updateTime() {
                    const now = new Date();
                    const timeString = now.toLocaleString('zh-CN');
                    document.title = '系统主页 - ' + timeString;
                }

                // 每秒更新一次时间
                setInterval(updateTime, 1000);
                updateTime();
            </script>
        </body>

        </html>