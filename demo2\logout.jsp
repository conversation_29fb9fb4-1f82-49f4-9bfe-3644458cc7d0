<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 解决中文乱码问题
    request.setCharacterEncoding("UTF-8");
    response.setCharacterEncoding("UTF-8");
    
    // 获取当前用户名
    String username = (String) session.getAttribute("username");
    
    // 清除Session中的所有数据
    session.invalidate();
    
    // 清除Cookie（可选，如果用户选择不记住密码的话）
    // 这里我们不清除Cookie，让用户下次登录时仍然可以自动填充
    
    // 重定向到登录页面，并显示退出成功信息
    response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode("您已安全退出系统！", "UTF-8"));
%>
