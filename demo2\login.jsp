<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
    <% // 解决中文乱码问题 request.setCharacterEncoding("UTF-8"); response.setCharacterEncoding("UTF-8"); // 获取Cookie中保存的用户名和密码
        String savedUsername="" ; String savedPassword="" ; Cookie[] cookies=request.getCookies(); if (cookies !=null) {
        for (Cookie cookie : cookies) { if ("username".equals(cookie.getName())) { savedUsername=cookie.getValue(); }
        else if ("password".equals(cookie.getName())) { savedPassword=cookie.getValue(); } } } // 获取错误信息 String
        errorMsg=request.getParameter("error"); %>
        <!DOCTYPE html>
        <html>

        <head>
            <meta charset="UTF-8">
            <title>用户登录</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f5f5f5;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                }

                .login-container {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    width: 300px;
                }

                .login-title {
                    text-align: center;
                    color: #333;
                    margin-bottom: 20px;
                }

                .form-group {
                    margin-bottom: 15px;
                }

                label {
                    display: block;
                    margin-bottom: 5px;
                    color: #555;
                }

                input[type="text"],
                input[type="password"] {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    box-sizing: border-box;
                }

                .checkbox-group {
                    margin: 15px 0;
                }

                .btn-group {
                    text-align: center;
                    margin-top: 20px;
                }

                input[type="submit"],
                input[type="reset"] {
                    padding: 10px 20px;
                    margin: 0 5px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                }

                input[type="submit"] {
                    background-color: #007bff;
                    color: white;
                }

                input[type="reset"] {
                    background-color: #6c757d;
                    color: white;
                }

                .error-msg {
                    color: red;
                    text-align: center;
                    margin-bottom: 15px;
                    font-size: 14px;
                }

                .register-link {
                    text-align: center;
                    margin-top: 15px;
                }

                .register-link a {
                    color: #007bff;
                    text-decoration: none;
                }
            </style>
        </head>

        <body>
            <div class="login-container">
                <h2 class="login-title">用户登录</h2>

                <% if (errorMsg !=null && !errorMsg.isEmpty()) { %>
                    <div class="error-msg">
                        <%= errorMsg %>
                    </div>
                    <% } %>

                        <form method="post" action="doLogin.jsp">
                            <div class="form-group">
                                <label for="username">用户名:</label>
                                <input type="text" id="username" name="username" value="<%= savedUsername %>" required>
                            </div>

                            <div class="form-group">
                                <label for="password">密码:</label>
                                <input type="password" id="password" name="password" value="<%= savedPassword %>"
                                    required>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <input type="checkbox" name="remember" value="true" <%=(savedUsername !=null &&
                                        !savedUsername.isEmpty()) ? "checked" : "" %>>
                                    记住用户名和密码
                                </label>
                            </div>

                            <div class="btn-group">
                                <input type="submit" value="登录">
                                <input type="reset" value="重置">
                            </div>
                        </form>

                        <div class="register-link">
                            <a href="register.html">还没有账号？点击注册</a>
                        </div>
            </div>
        </body>

        </html>