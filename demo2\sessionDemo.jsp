<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 解决中文乱码问题
    request.setCharacterEncoding("UTF-8");
    response.setCharacterEncoding("UTF-8");
    
    // 检查用户是否已登录
    String sessionUsername = (String) session.getAttribute("username");
    if (sessionUsername == null) {
        response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode("请先登录！", "UTF-8"));
        return;
    }
    
    // 获取访问次数
    Integer visitCount = (Integer) session.getAttribute("visitCount");
    if (visitCount == null) {
        visitCount = 1;
    } else {
        visitCount++;
    }
    session.setAttribute("visitCount", visitCount);
    
    // 获取所有Cookie信息
    Cookie[] cookies = request.getCookies();
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Session演示页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .info-box {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .back-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Session和Cookie演示页面</h1>
        </div>
        
        <div class="info-box">
            <h3>Session信息：</h3>
            <div class="info-item">
                <strong>当前用户：</strong><%= sessionUsername %>
            </div>
            <div class="info-item">
                <strong>Session ID：</strong><%= session.getId() %>
            </div>
            <div class="info-item">
                <strong>访问次数：</strong><%= visitCount %>
            </div>
            <div class="info-item">
                <strong>Session创建时间：</strong><%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(session.getCreationTime())) %>
            </div>
            <div class="info-item">
                <strong>最后访问时间：</strong><%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(session.getLastAccessedTime())) %>
            </div>
            <div class="info-item">
                <strong>Session最大非活动间隔：</strong><%= session.getMaxInactiveInterval() %> 秒
            </div>
            <div class="info-item">
                <strong>Session是否为新创建：</strong><%= session.isNew() ? "是" : "否" %>
            </div>
        </div>
        
        <div class="info-box">
            <h3>Cookie信息：</h3>
            <% if (cookies != null && cookies.length > 0) { %>
                <table>
                    <tr>
                        <th>Cookie名称</th>
                        <th>Cookie值</th>
                        <th>最大生存时间</th>
                        <th>路径</th>
                    </tr>
                    <% for (Cookie cookie : cookies) { %>
                        <tr>
                            <td><%= cookie.getName() %></td>
                            <td><%= cookie.getValue() %></td>
                            <td><%= cookie.getMaxAge() == -1 ? "会话结束时删除" : cookie.getMaxAge() + " 秒" %></td>
                            <td><%= cookie.getPath() != null ? cookie.getPath() : "默认路径" %></td>
                        </tr>
                    <% } %>
                </table>
            <% } else { %>
                <p>当前没有Cookie信息</p>
            <% } %>
        </div>
        
        <div class="info-box">
            <h3>Request对象信息：</h3>
            <div class="info-item">
                <strong>请求方法：</strong><%= request.getMethod() %>
            </div>
            <div class="info-item">
                <strong>请求URI：</strong><%= request.getRequestURI() %>
            </div>
            <div class="info-item">
                <strong>请求URL：</strong><%= request.getRequestURL() %>
            </div>
            <div class="info-item">
                <strong>客户端IP：</strong><%= request.getRemoteAddr() %>
            </div>
            <div class="info-item">
                <strong>用户代理：</strong><%= request.getHeader("User-Agent") %>
            </div>
            <div class="info-item">
                <strong>字符编码：</strong><%= request.getCharacterEncoding() %>
            </div>
        </div>
        
        <a href="index.jsp" class="back-btn">返回主页</a>
    </div>
</body>
</html>
