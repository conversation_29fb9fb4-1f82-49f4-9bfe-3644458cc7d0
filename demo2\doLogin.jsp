<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
    <% // 解决中文乱码问题 request.setCharacterEncoding("UTF-8"); response.setCharacterEncoding("UTF-8"); // 使用Request对象获取表单数据
        String username=request.getParameter("username"); String password=request.getParameter("password"); String
        remember=request.getParameter("remember"); // 验证用户名和密码 boolean isValid=false; if ("admin".equals(username)
        && "123456" .equals(password)) { isValid=true; } if (isValid) { // 登录成功 // 使用Session保存用户名
        session.setAttribute("username", username); session.setAttribute("loginTime", new java.util.Date()); //
        如果选择了记住密码，使用Cookie保存用户名和密码 if ("true".equals(remember)) { Cookie usernameCookie=new <PERSON><PERSON>("username",
        username); Cookie passwordCookie=new <PERSON>ie("password", password); // 设置Cookie有效期为7天 usernameCookie.setMaxAge(7
        * 24 * 60 * 60); passwordCookie.setMaxAge(7 * 24 * 60 * 60); // 设置Cookie路径
        usernameCookie.setPath(request.getContextPath()); passwordCookie.setPath(request.getContextPath()); //
        添加Cookie到响应中 response.addCookie(usernameCookie); response.addCookie(passwordCookie); } else { //
        如果没有选择记住密码，清除已存在的Cookie Cookie usernameCookie=new Cookie("username", "" ); Cookie passwordCookie=new
        Cookie("password", "" ); usernameCookie.setMaxAge(0); passwordCookie.setMaxAge(0);
        usernameCookie.setPath(request.getContextPath()); passwordCookie.setPath(request.getContextPath());
        response.addCookie(usernameCookie); response.addCookie(passwordCookie); } // 使用Response对象重定向到index.jsp
        response.sendRedirect("index.jsp"); } else { // 登录失败，重定向回登录页面并显示错误信息 String errorMsg="" ; if (username==null ||
        username.trim().isEmpty()) { errorMsg="请输入用户名！" ; } else if (password==null || password.trim().isEmpty()) {
        errorMsg="请输入密码！" ; } else { errorMsg="用户名或密码错误！请检查用户名是否为admin，密码是否为123456。" ; } // 使用Response对象重定向回登录页面
        response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode(errorMsg, " UTF-8")); } %>