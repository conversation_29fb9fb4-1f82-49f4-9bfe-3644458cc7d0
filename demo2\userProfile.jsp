<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 解决中文乱码问题
    request.setCharacterEncoding("UTF-8");
    response.setCharacterEncoding("UTF-8");
    
    // 检查用户是否已登录
    String sessionUsername = (String) session.getAttribute("username");
    if (sessionUsername == null) {
        response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode("请先登录！", "UTF-8"));
        return;
    }
    
    // 模拟用户资料数据（实际项目中应该从数据库获取）
    String userRole = "admin".equals(sessionUsername) ? "系统管理员" : "普通用户";
    String userEmail = "admin".equals(sessionUsername) ? "<EMAIL>" : sessionUsername + "@example.com";
    java.util.Date loginTime = (java.util.Date) session.getAttribute("loginTime");
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>个人资料</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #17a2b8;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .profile-card {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .profile-item {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }
        .profile-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .profile-value {
            color: #6c757d;
            font-size: 16px;
        }
        .back-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
        }
        .back-btn:hover {
            background-color: #0056b3;
        }
        .edit-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .edit-btn:hover {
            background-color: #218838;
        }
        .avatar {
            width: 80px;
            height: 80px;
            background-color: #17a2b8;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>个人资料</h1>
        </div>
        
        <div class="profile-card">
            <div class="avatar">
                <%= sessionUsername.substring(0, 1).toUpperCase() %>
            </div>
            
            <div class="profile-item">
                <div class="profile-label">用户名：</div>
                <div class="profile-value"><%= sessionUsername %></div>
            </div>
            
            <div class="profile-item">
                <div class="profile-label">用户角色：</div>
                <div class="profile-value"><%= userRole %></div>
            </div>
            
            <div class="profile-item">
                <div class="profile-label">电子邮箱：</div>
                <div class="profile-value"><%= userEmail %></div>
            </div>
            
            <% if (loginTime != null) { %>
            <div class="profile-item">
                <div class="profile-label">本次登录时间：</div>
                <div class="profile-value"><%= new java.text.SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(loginTime) %></div>
            </div>
            <% } %>
            
            <div class="profile-item">
                <div class="profile-label">Session ID：</div>
                <div class="profile-value"><%= session.getId() %></div>
            </div>
            
            <div class="profile-item">
                <div class="profile-label">账户状态：</div>
                <div class="profile-value" style="color: #28a745;">正常</div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.jsp" class="back-btn">返回主页</a>
                <a href="#" class="edit-btn" onclick="alert('编辑功能暂未实现')">编辑资料</a>
            </div>
        </div>
    </div>
</body>
</html>
