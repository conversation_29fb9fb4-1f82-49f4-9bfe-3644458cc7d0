<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    // 解决中文乱码问题
    request.setCharacterEncoding("UTF-8");
    response.setCharacterEncoding("UTF-8");
    
    // 检查用户是否已登录
    String sessionUsername = (String) session.getAttribute("username");
    if (sessionUsername == null) {
        response.sendRedirect("login.jsp?error=" + java.net.URLEncoder.encode("请先登录！", "UTF-8"));
        return;
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统信息</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background-color: #6f42c1;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .info-section {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #6f42c1;
        }
        .back-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
        .back-btn:hover {
            background-color: #0056b3;
        }
        .section-title {
            color: #6f42c1;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>系统信息</h1>
        </div>
        
        <div class="info-section">
            <h3 class="section-title">服务器信息</h3>
            <div class="info-item">
                <strong>服务器信息：</strong><%= application.getServerInfo() %>
            </div>
            <div class="info-item">
                <strong>Servlet版本：</strong><%= application.getMajorVersion() %>.<%= application.getMinorVersion() %>
            </div>
            <div class="info-item">
                <strong>Java版本：</strong><%= System.getProperty("java.version") %>
            </div>
            <div class="info-item">
                <strong>操作系统：</strong><%= System.getProperty("os.name") %> <%= System.getProperty("os.version") %>
            </div>
            <div class="info-item">
                <strong>系统架构：</strong><%= System.getProperty("os.arch") %>
            </div>
        </div>
        
        <div class="info-section">
            <h3 class="section-title">应用程序信息</h3>
            <div class="info-item">
                <strong>应用程序名称：</strong>JSP登录系统演示
            </div>
            <div class="info-item">
                <strong>上下文路径：</strong><%= request.getContextPath() %>
            </div>
            <div class="info-item">
                <strong>应用程序真实路径：</strong><%= application.getRealPath("/") %>
            </div>
            <div class="info-item">
                <strong>当前时间：</strong><%= new java.text.SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(new java.util.Date()) %>
            </div>
        </div>
        
        <div class="info-section">
            <h3 class="section-title">内存使用情况</h3>
            <%
                Runtime runtime = Runtime.getRuntime();
                long totalMemory = runtime.totalMemory();
                long freeMemory = runtime.freeMemory();
                long usedMemory = totalMemory - freeMemory;
                long maxMemory = runtime.maxMemory();
            %>
            <div class="info-item">
                <strong>总内存：</strong><%= String.format("%.2f MB", totalMemory / 1024.0 / 1024.0) %>
            </div>
            <div class="info-item">
                <strong>已使用内存：</strong><%= String.format("%.2f MB", usedMemory / 1024.0 / 1024.0) %>
            </div>
            <div class="info-item">
                <strong>空闲内存：</strong><%= String.format("%.2f MB", freeMemory / 1024.0 / 1024.0) %>
            </div>
            <div class="info-item">
                <strong>最大可用内存：</strong><%= String.format("%.2f MB", maxMemory / 1024.0 / 1024.0) %>
            </div>
        </div>
        
        <div class="info-section">
            <h3 class="section-title">JSP内置对象演示</h3>
            <div class="info-item">
                <strong>Request对象：</strong>用于获取客户端请求信息
            </div>
            <div class="info-item">
                <strong>Response对象：</strong>用于向客户端发送响应
            </div>
            <div class="info-item">
                <strong>Session对象：</strong>用于保存用户会话信息
            </div>
            <div class="info-item">
                <strong>Application对象：</strong>用于获取应用程序信息
            </div>
            <div class="info-item">
                <strong>Cookie对象：</strong>用于在客户端保存少量数据
            </div>
        </div>
        
        <a href="index.jsp" class="back-btn">返回主页</a>
    </div>
</body>
</html>
